# Antosa Architect

🏗️ **Professional Architecture & Design Services**

Mewujudkan Desain Impian <PERSON><PERSON> dengan layanan arsitektur profesional dan berkualitas tinggi.

## 🌟 Features

- **Modern Design**: Clean and professional website design
- **Responsive Layout**: Optimized for all devices
- **Portfolio Showcase**: Display of architectural projects
- **Contact Integration**: Easy client communication
- **SEO Optimized**: Better search engine visibility

## 🚀 Live Demo

Visit our website: [Antosa Architect](https://your-railway-url.railway.app)

## 🛠️ Tech Stack

- **Backend**: PHP (Custom MVC Framework)
- **Frontend**: HTML5, CSS3, JavaScript
- **Styling**: Tailwind CSS
- **Deployment**: Railway
- **Version Control**: Git & GitHub

## 📁 Project Structure

```
antosa-architect/
├── app/
│   ├── controllers/     # Application controllers
│   └── helpers/         # Helper functions
├── config/
│   └── app.php         # Application configuration
├── public/
│   ├── assets/         # Static assets (CSS, JS, Images)
│   └── index.php       # Application entry point
├── routes/
│   └── web.php         # Route definitions
├── views/
│   ├── layouts/        # Layout templates
│   └── *.php          # View templates
├── railway.json        # Railway deployment configuration
├── nixpacks.toml       # Nixpacks build configuration
├── Procfile           # Process file for Railway
└── index.php          # Root entry point
```

## 🚀 Deployment

This project is configured for automatic deployment via GitHub to Railway:

1. **Connect GitHub**: Link your GitHub repository to Railway
2. **Automatic Deployment**: Any push to main branch triggers deployment
3. **Custom Domain**: Can be configured in Railway dashboard
4. **Environment Variables**: Set in Railway dashboard

### Railway Deployment Steps

1. **Create Railway Account**: Sign up at [railway.app](https://railway.app)
2. **Connect GitHub**: Link your GitHub repository
3. **Deploy**: Railway will automatically detect PHP and deploy
4. **Configure Domain**: Set up custom domain if needed

### Environment Variables

Set these in Railway dashboard if needed:
- `RAILWAY_ENVIRONMENT`: production
- `APP_ENV`: production
- `APP_DEBUG`: false
- `COMPANY_EMAIL`: Contact email
- `COMPANY_PHONE`: Contact phone

## 📞 Contact Information

- **Email**: <EMAIL>
- **Phone**: +62 851 8952 3863
- **Address**: Bernady Land, Cluster Camelia Blok E6, Puring, Slawu, Kec. Patrang, Kabupaten Jember, Jawa Timur 68116

## 📱 Social Media

- [Instagram](https://instagram.com/antosa_architect)
- [Facebook](https://facebook.com/antosa.architect)
- [Twitter](https://twitter.com/antosa_architect)
- [LinkedIn](https://linkedin.com/company/antosa-architect)

## 📄 License

© 2024 Antosa Architect. All rights reserved.
