name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    name: Test & Validate
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
        extensions: mbstring, xml, ctype, json, tokenizer
        coverage: none

    - name: Validate PHP syntax
      run: |
        echo "🔍 Validating PHP syntax..."
        find . -name "*.php" -not -path "./vendor/*" -exec php -l {} \; | grep -v "No syntax errors"
        echo "✅ PHP syntax validation completed"

    - name: Check project structure
      run: |
        echo "🏗️ Checking project structure..."
        ls -la
        echo ""
        echo "📁 Checking public directory..."
        ls -la public/
        echo ""
        echo "📁 Checking app directory..."
        ls -la app/
        echo ""
        echo "📁 Checking config files..."
        ls -la config/
        echo ""
        echo "🚀 Checking Railway configuration..."
        if [ -f "railway.json" ]; then
          echo "✅ railway.json found"
          cat railway.json
        else
          echo "❌ railway.json not found"
        fi
        echo ""
        if [ -f "nixpacks.toml" ]; then
          echo "✅ nixpacks.toml found"
          cat nixpacks.toml
        else
          echo "❌ nixpacks.toml not found"
        fi

    - name: Validate configuration files
      run: |
        echo "🔧 Validating configuration files..."
        php -f config/app.php
        echo "✅ Configuration validation completed"

    - name: Check routes
      run: |
        echo "🛣️ Validating routes..."
        php -r "
        require_once 'config/app.php';
        \$routes = require_once 'routes/web.php';
        echo 'Routes loaded: ' . count(\$routes) . PHP_EOL;
        foreach(\$routes as \$path => \$config) {
          echo '- ' . \$path . ' => ' . \$config['controller'] . '@' . \$config['action'] . PHP_EOL;
        }
        "
        echo "✅ Routes validation completed"

  deploy-notification:
    name: Deployment Status
    runs-on: ubuntu-latest
    needs: test
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'

    steps:
    - name: Deployment Info
      run: |
        echo "🚀 Deployment triggered for Railway"
        echo "📦 Branch: ${{ github.ref_name }}"
        echo "👤 Author: ${{ github.actor }}"
        echo "💬 Commit: ${{ github.event.head_commit.message }}"
        echo ""
        echo "ℹ️  Railway will automatically deploy this push."
        echo "🔗 Check your Railway dashboard for deployment status."
